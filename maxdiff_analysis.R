# 安装和加载必要的包
if (!require(choicemodel)) {
  install.packages("choicemodel")
}
library(choicemodel)

# 构造MaxDiff样例数据
set.seed(123)

# 创建属性列表（例如：品牌偏好研究）
attributes <- c("Apple", "Samsung", "Huawei", "Xiaomi", "OnePlus", "Google")

# 生成MaxDiff设计矩阵
n_respondents <- 100
n_tasks <- 8
n_items_per_task <- 4

# 创建MaxDiff数据框
maxdiff_data <- data.frame()

for (resp in 1:n_respondents) {
  for (task in 1:n_tasks) {
    # 随机选择4个属性
    selected_items <- sample(attributes, n_items_per_task)
    
    # 模拟最佳和最差选择
    best_item <- sample(selected_items, 1)
    worst_item <- sample(selected_items[selected_items != best_item], 1)
    
    # 创建任务数据
    task_data <- data.frame(
      respondent = resp,
      task = task,
      item = selected_items,
      best = ifelse(selected_items == best_item, 1, 0),
      worst = ifelse(selected_items == worst_item, 1, 0)
    )
    
    maxdiff_data <- rbind(maxdiff_data, task_data)
  }
}

# 查看数据结构
head(maxdiff_data, 12)
cat("数据维度:", dim(maxdiff_data), "\n")

# 使用choicemodel进行MaxDiff分析
# 创建选择矩阵
choice_data <- maxdiff_data
choice_data$choice <- ifelse(choice_data$best == 1, 1, 
                           ifelse(choice_data$worst == 1, -1, 0))

# 运行MaxDiff模型
model <- choicemodel(
  data = choice_data,
  choice = "choice",
  id = "respondent",
  task = "task",
  alternative = "item"
)

# 显示结果
summary(model)

# 计算效用值
utilities <- coef(model)
print("属性效用值:")
print(utilities)

# 可视化结果
if (require(ggplot2)) {
  library(ggplot2)
  
  util_df <- data.frame(
    attribute = names(utilities),
    utility = as.numeric(utilities)
  )
  
  ggplot(util_df, aes(x = reorder(attribute, utility), y = utility)) +
    geom_bar(stat = "identity", fill = "steelblue") +
    coord_flip() +
    labs(title = "MaxDiff分析结果 - 属性效用值",
         x = "属性",
         y = "效用值") +
    theme_minimal()
}