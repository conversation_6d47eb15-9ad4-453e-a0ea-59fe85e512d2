# MaxDiff分析 - 合并版本
# 设置CRAN镜像
options(repos = c(CRAN = "https://cran.rstudio.com/"))

# 安装和加载必要的包
if (!require(ggplot2)) {
  install.packages("ggplot2")
}
library(ggplot2)

# 构造MaxDiff样例数据
set.seed(123)

# 创建属性列表（例如：品牌偏好研究）
attributes <- c("Apple", "Samsung", "Huawei", "Xiaomi", "OnePlus", "Google")

# 生成MaxDiff设计矩阵
n_respondents <- 100
n_tasks <- 8
n_items_per_task <- 4

# 创建MaxDiff数据框
maxdiff_data <- data.frame()

for (resp in 1:n_respondents) {
  for (task in 1:n_tasks) {
    # 随机选择4个属性
    selected_items <- sample(attributes, n_items_per_task)
    
    # 模拟最佳和最差选择
    best_item <- sample(selected_items, 1)
    worst_item <- sample(selected_items[selected_items != best_item], 1)
    
    # 创建任务数据
    task_data <- data.frame(
      respondent = resp,
      task = task,
      item = selected_items,
      best = ifelse(selected_items == best_item, 1, 0),
      worst = ifelse(selected_items == worst_item, 1, 0)
    )
    
    maxdiff_data <- rbind(maxdiff_data, task_data)
  }
}

# 查看数据结构
head(maxdiff_data, 12)
cat("数据维度:", dim(maxdiff_data), "\n")

# 使用基础R进行MaxDiff分析
# 创建选择矩阵
choice_data <- maxdiff_data
choice_data$choice <- ifelse(choice_data$best == 1, 1, 
                           ifelse(choice_data$worst == 1, -1, 0))

# 计算每个属性的得分
attribute_scores <- aggregate(choice_data$choice, 
                            by = list(choice_data$item), 
                            FUN = sum)
names(attribute_scores) <- c("attribute", "score")

# 计算效用值（标准化得分）
utilities <- attribute_scores$score
names(utilities) <- attribute_scores$attribute

print("属性得分:")
print(attribute_scores)
print("属性效用值:")
print(utilities)

# 可视化结果
util_df <- data.frame(
  attribute = names(utilities),
  utility = as.numeric(utilities)
)

# 创建条形图
p <- ggplot(util_df, aes(x = reorder(attribute, utility), y = utility)) +
  geom_bar(stat = "identity", fill = "steelblue") +
  coord_flip() +
  labs(title = "MaxDiff分析结果 - 属性效用值",
       x = "属性",
       y = "效用值") +
  theme_minimal()

print(p)

# 保存图表
ggsave("combined_maxdiff_results.png", plot = p, width = 10, height = 6, dpi = 300)

# === 分析摘要部分 ===
cat("\n=== MaxDiff分析完成 ===\n")
cat("样本大小:", n_respondents, "个受访者\n")
cat("任务数量:", n_tasks, "个任务/受访者\n")
cat("总观察数:", nrow(maxdiff_data), "\n")

# 计算相对重要性
if (exists("utilities")) {
  # 转换为正值并计算相对重要性
  shifted_utils <- utilities - min(utilities)
  relative_importance <- shifted_utils / sum(shifted_utils) * 100
  
  cat("\n相对重要性 (%):\n")
  for (i in 1:length(relative_importance)) {
    cat(sprintf("%s: %.1f%%\n", names(relative_importance)[i], relative_importance[i]))
  }
}

cat("\n分析完成！图表已保存为 'combined_maxdiff_results.png'\n")
